"""
Composio Connection Status Monitor

This service monitors the status of Composio OAuth connections and automatically
transitions from Phase 1 (connection creation) to Phase 2 (tool calling) when
a user completes the OAuth flow.

It provides both polling-based monitoring and webhook handling capabilities.
"""

import asyncio
import json
from typing import Dict, Set, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from utils.logger import logger
from services.composio_connected_accounts import (
    composio_connected_accounts_service,
    UserConnectionInfo,
)
from services.composio_url_manager import (
    composio_url_manager,
    MCPPhase,
)


@dataclass
class PendingConnection:
    """Represents a connection that's waiting for OAuth completion"""
    
    user_id: str
    app_key: str
    started_at: datetime
    last_checked: datetime
    check_count: int = 0
    max_checks: int = 60  # 5 minutes with 5-second intervals
    
    def is_expired(self) -> bool:
        """Check if this pending connection has expired"""
        return self.check_count >= self.max_checks


class ComposioConnectionMonitor:
    """Monitors Composio connection status and handles phase transitions"""
    
    def __init__(self):
        self._pending_connections: Dict[str, PendingConnection] = {}
        self._monitoring_task: Optional[asyncio.Task] = None
        self._is_monitoring = False
        self._poll_interval = 5  # seconds
        self._connection_callbacks: Dict[str, Callable] = {}
    
    def _get_pending_key(self, user_id: str, app_key: str) -> str:
        """Generate key for pending connections"""
        return f"{user_id}:{app_key}"
    
    def add_pending_connection(self, user_id: str, app_key: str) -> None:
        """
        Add a connection to monitor for OAuth completion.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
        """
        key = self._get_pending_key(user_id, app_key)
        
        pending = PendingConnection(
            user_id=user_id,
            app_key=app_key,
            started_at=datetime.now(),
            last_checked=datetime.now()
        )
        
        self._pending_connections[key] = pending
        logger.info(f"Added pending connection to monitor: {user_id}:{app_key}")
        
        # Start monitoring if not already running
        if not self._is_monitoring:
            self.start_monitoring()
    
    def remove_pending_connection(self, user_id: str, app_key: str) -> None:
        """
        Remove a connection from monitoring.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
        """
        key = self._get_pending_key(user_id, app_key)
        
        if key in self._pending_connections:
            del self._pending_connections[key]
            logger.info(f"Removed pending connection from monitor: {user_id}:{app_key}")
    
    def register_connection_callback(
        self, user_id: str, app_key: str, callback: Callable[[bool, Optional[str]], None]
    ) -> None:
        """
        Register a callback to be called when connection status changes.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
            callback: Function to call with (success, connected_account_id)
        """
        key = self._get_pending_key(user_id, app_key)
        self._connection_callbacks[key] = callback
        logger.info(f"Registered callback for connection: {user_id}:{app_key}")
    
    async def _check_pending_connection(self, pending: PendingConnection) -> bool:
        """
        Check if a pending connection has been completed.
        
        Args:
            pending: PendingConnection to check
            
        Returns:
            True if connection was completed, False otherwise
        """
        try:
            # Check connection status
            is_connected, connected_account_id = await composio_connected_accounts_service.check_connection_status(
                pending.user_id, pending.app_key
            )
            
            pending.last_checked = datetime.now()
            pending.check_count += 1
            
            if is_connected and connected_account_id:
                logger.info(f"Connection completed: {pending.user_id}:{pending.app_key} -> {connected_account_id}")
                
                # Transition to tool calling phase
                success, tool_calling_config = await composio_url_manager.transition_to_tool_calling(
                    pending.user_id, pending.app_key
                )
                
                if success:
                    logger.info(f"Successfully transitioned to tool calling phase: {pending.user_id}:{pending.app_key}")
                    
                    # Call registered callback if exists
                    key = self._get_pending_key(pending.user_id, pending.app_key)
                    if key in self._connection_callbacks:
                        try:
                            callback = self._connection_callbacks[key]
                            callback(True, connected_account_id)
                            del self._connection_callbacks[key]
                        except Exception as e:
                            logger.error(f"Error calling connection callback: {str(e)}")
                    
                    return True
                else:
                    logger.error(f"Failed to transition to tool calling phase: {pending.user_id}:{pending.app_key}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking pending connection {pending.user_id}:{pending.app_key}: {str(e)}")
            return False
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop that checks pending connections"""
        logger.info("Started connection monitoring loop")
        
        while self._is_monitoring:
            try:
                # Get list of pending connections to check
                pending_to_check = list(self._pending_connections.values())
                
                if not pending_to_check:
                    # No pending connections, sleep longer
                    await asyncio.sleep(self._poll_interval * 2)
                    continue
                
                # Check each pending connection
                completed_keys = []
                expired_keys = []
                
                for pending in pending_to_check:
                    key = self._get_pending_key(pending.user_id, pending.app_key)
                    
                    if pending.is_expired():
                        logger.warning(f"Pending connection expired: {pending.user_id}:{pending.app_key}")
                        expired_keys.append(key)
                        
                        # Call callback with failure
                        if key in self._connection_callbacks:
                            try:
                                callback = self._connection_callbacks[key]
                                callback(False, None)
                                del self._connection_callbacks[key]
                            except Exception as e:
                                logger.error(f"Error calling expired connection callback: {str(e)}")
                        
                        continue
                    
                    # Check if connection is completed
                    is_completed = await self._check_pending_connection(pending)
                    
                    if is_completed:
                        completed_keys.append(key)
                
                # Remove completed and expired connections
                for key in completed_keys + expired_keys:
                    if key in self._pending_connections:
                        del self._pending_connections[key]
                
                if completed_keys:
                    logger.info(f"Completed connections: {len(completed_keys)}")
                if expired_keys:
                    logger.warning(f"Expired connections: {len(expired_keys)}")
                
                # Sleep before next check
                await asyncio.sleep(self._poll_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                await asyncio.sleep(self._poll_interval)
    
    def start_monitoring(self) -> None:
        """Start the connection monitoring loop"""
        if self._is_monitoring:
            logger.warning("Connection monitoring is already running")
            return
        
        self._is_monitoring = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started connection monitoring")
    
    def stop_monitoring(self) -> None:
        """Stop the connection monitoring loop"""
        if not self._is_monitoring:
            logger.warning("Connection monitoring is not running")
            return
        
        self._is_monitoring = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            self._monitoring_task = None
        
        logger.info("Stopped connection monitoring")
    
    async def wait_for_connection(
        self, user_id: str, app_key: str, timeout_seconds: int = 300
    ) -> tuple[bool, Optional[str]]:
        """
        Wait for a specific connection to be completed.
        
        This is a convenience method that adds the connection to monitoring
        and waits for completion or timeout.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
            timeout_seconds: Maximum time to wait (default: 5 minutes)
            
        Returns:
            Tuple of (success, connected_account_id)
        """
        logger.info(f"Waiting for connection: {user_id}:{app_key} (timeout: {timeout_seconds}s)")
        
        # Use the connected accounts service wait method
        return await composio_connected_accounts_service.wait_for_connection(
            user_id, app_key, timeout_seconds
        )
    
    def get_pending_connections(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all pending connections.
        
        Returns:
            Dictionary with pending connection information
        """
        result = {}
        
        for key, pending in self._pending_connections.items():
            result[key] = {
                "user_id": pending.user_id,
                "app_key": pending.app_key,
                "started_at": pending.started_at.isoformat(),
                "last_checked": pending.last_checked.isoformat(),
                "check_count": pending.check_count,
                "max_checks": pending.max_checks,
                "is_expired": pending.is_expired()
            }
        
        return result
    
    async def handle_webhook(self, webhook_data: Dict[str, Any]) -> bool:
        """
        Handle webhook notifications from Composio about connection status changes.
        
        This is an alternative to polling that can provide faster response times.
        
        Args:
            webhook_data: Webhook payload from Composio
            
        Returns:
            True if webhook was processed successfully
        """
        try:
            # Parse webhook data (format depends on Composio webhook structure)
            event_type = webhook_data.get("event_type")
            
            if event_type == "connected_account.created":
                # Extract user and app information from webhook
                user_id = webhook_data.get("user_id")  # This might be entity_id
                app_key = webhook_data.get("app_key") or webhook_data.get("toolkit_slug")
                connected_account_id = webhook_data.get("connected_account_id")
                
                if user_id and app_key and connected_account_id:
                    logger.info(f"Webhook: Connection created for {user_id}:{app_key}")
                    
                    # Check if we're monitoring this connection
                    key = self._get_pending_key(user_id, app_key)
                    
                    if key in self._pending_connections:
                        # Trigger immediate transition
                        success, tool_calling_config = await composio_url_manager.transition_to_tool_calling(
                            user_id, app_key
                        )
                        
                        if success:
                            # Remove from pending and call callback
                            self.remove_pending_connection(user_id, app_key)
                            
                            if key in self._connection_callbacks:
                                try:
                                    callback = self._connection_callbacks[key]
                                    callback(True, connected_account_id)
                                    del self._connection_callbacks[key]
                                except Exception as e:
                                    logger.error(f"Error calling webhook callback: {str(e)}")
                            
                            return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error handling webhook: {str(e)}")
            return False


# Global instance
composio_connection_monitor = ComposioConnectionMonitor()
