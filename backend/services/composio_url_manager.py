"""
Composio Two-Phase URL Manager

This service manages the transition between the two phases of Composio MCP integration:

Phase 1: Connection Creation
- URL: https://mcp.composio.dev/composio/server/{server_id}?user_id={user_id}&include_composio_helper_actions=true
- Purpose: Guide user through OAuth, create connected account

Phase 2: Tool Calling  
- URL: https://mcp.composio.dev/composio/server/{server_id}/mcp?connected_account_id={account_id}
- Purpose: Actual MCP tool execution with user's specific connection

This service handles the URL generation, storage, and transition between phases.
"""

import json
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from utils.logger import logger
from supabase import create_client, Client
from utils.config import config
from services.composio_connected_accounts import (
    composio_connected_accounts_service,
    UserConnectionInfo,
)


class MCPPhase(Enum):
    """Enum for MCP connection phases"""
    CONNECTION_CREATION = "connection_creation"
    TOOL_CALLING = "tool_calling"


@dataclass
class MCPURLConfig:
    """Configuration for MCP URL in different phases"""
    
    app_key: str
    server_id: str
    user_id: str
    phase: MCPPhase
    url: str
    connected_account_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ComposioURLManager:
    """Manages two-phase URL generation and transitions for Composio MCP"""
    
    def __init__(self):
        self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_SERVICE_KEY)
        self._server_registry: Dict[str, str] = {}
        self._load_server_registry()
    
    def _load_server_registry(self):
        """Load server IDs from the configuration file"""
        try:
            import os
            config_path = os.path.join(
                os.path.dirname(__file__), 
                "..", 
                "config", 
                "composio_mcp_servers.json"
            )
            
            with open(config_path, 'r') as f:
                config_data = json.load(f)
                
            for app_key, server_info in config_data.get("servers", {}).items():
                self._server_registry[app_key] = server_info["server_id"]
                
            logger.info(f"Loaded {len(self._server_registry)} server configurations")
            
        except Exception as e:
            logger.error(f"Error loading server registry: {str(e)}")
            # Fallback to known servers
            self._server_registry = {
                "gmail": "3adcafb4-4a7d-4b97-bb26-3be782883e06",
                "notion": "5b1d27a8-c112-470a-af56-3f1c40991ebe"
            }
    
    def _get_server_id(self, app_key: str) -> Optional[str]:
        """Get server ID for an app"""
        return self._server_registry.get(app_key)
    
    def generate_connection_creation_url(self, app_key: str, user_id: str) -> Optional[MCPURLConfig]:
        """
        Generate Phase 1 URL for connection creation (with helper actions).
        
        Args:
            app_key: App key (e.g., "gmail", "slack")
            user_id: Atlas user ID
            
        Returns:
            MCPURLConfig for connection creation phase
        """
        server_id = self._get_server_id(app_key)
        if not server_id:
            logger.error(f"No server ID found for app: {app_key}")
            return None
        
        # Phase 1: Connection creation URL with helper actions
        url = f"https://mcp.composio.dev/composio/server/{server_id}?user_id={user_id}&include_composio_helper_actions=true"
        
        config = MCPURLConfig(
            app_key=app_key,
            server_id=server_id,
            user_id=user_id,
            phase=MCPPhase.CONNECTION_CREATION,
            url=url,
            connected_account_id=None,
            metadata={
                "created_at": "now()",
                "status": "pending_connection",
                "description": f"Connection creation URL for {app_key}"
            }
        )
        
        logger.info(f"Generated connection creation URL for {app_key}: {url}")
        return config
    
    def generate_tool_calling_url(
        self, app_key: str, user_id: str, connected_account_id: str
    ) -> Optional[MCPURLConfig]:
        """
        Generate Phase 2 URL for tool calling (with connected account ID).
        
        Args:
            app_key: App key (e.g., "gmail", "slack")
            user_id: Atlas user ID
            connected_account_id: Composio connected account ID
            
        Returns:
            MCPURLConfig for tool calling phase
        """
        server_id = self._get_server_id(app_key)
        if not server_id:
            logger.error(f"No server ID found for app: {app_key}")
            return None
        
        # Phase 2: Tool calling URL with connected account ID
        url = f"https://mcp.composio.dev/composio/server/{server_id}/mcp?connected_account_id={connected_account_id}"
        
        config = MCPURLConfig(
            app_key=app_key,
            server_id=server_id,
            user_id=user_id,
            phase=MCPPhase.TOOL_CALLING,
            url=url,
            connected_account_id=connected_account_id,
            metadata={
                "created_at": "now()",
                "status": "connected",
                "description": f"Tool calling URL for {app_key}",
                "connected_account_id": connected_account_id
            }
        )
        
        logger.info(f"Generated tool calling URL for {app_key}: {url}")
        return config
    
    async def store_mcp_config(self, user_id: str, url_config: MCPURLConfig) -> bool:
        """
        Store MCP configuration in Supabase agents table.
        
        Args:
            user_id: Atlas user ID
            url_config: MCP URL configuration
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get user's default agent
            agent_result = self.supabase.table("agents").select("*").eq("user_id", user_id).limit(1).execute()
            
            if not agent_result.data:
                logger.error(f"No agent found for user {user_id}")
                return False
            
            default_agent = agent_result.data[0]
            agent_id = default_agent["agent_id"]
            current_custom_mcps = default_agent.get("custom_mcps", [])
            
            # Create MCP config entry
            mcp_config = {
                "name": url_config.app_key.title(),
                "type": "http",
                "config": {"url": url_config.url},
                "enabledTools": [],
                "metadata": {
                    "app_key": url_config.app_key,
                    "server_id": url_config.server_id,
                    "phase": url_config.phase.value,
                    "connected_account_id": url_config.connected_account_id,
                    **(url_config.metadata or {})
                }
            }
            
            # Check if config for this app already exists
            existing_index = None
            for i, existing_config in enumerate(current_custom_mcps):
                if existing_config.get("metadata", {}).get("app_key") == url_config.app_key:
                    existing_index = i
                    break
            
            if existing_index is not None:
                # Update existing config
                current_custom_mcps[existing_index] = mcp_config
                logger.info(f"Updated existing MCP config for {url_config.app_key}")
            else:
                # Add new config
                current_custom_mcps.append(mcp_config)
                logger.info(f"Added new MCP config for {url_config.app_key}")
            
            # Update agent in database
            update_result = self.supabase.table("agents").update({
                "custom_mcps": current_custom_mcps
            }).eq("agent_id", agent_id).execute()
            
            if update_result.data:
                logger.info(f"Successfully stored MCP config for {url_config.app_key} in phase {url_config.phase.value}")
                return True
            else:
                logger.error(f"Failed to update agent {agent_id} with MCP config")
                return False
                
        except Exception as e:
            logger.error(f"Error storing MCP config: {str(e)}")
            return False
    
    async def transition_to_tool_calling(self, user_id: str, app_key: str) -> Tuple[bool, Optional[MCPURLConfig]]:
        """
        Transition from connection creation phase to tool calling phase.
        
        This method:
        1. Checks if user has an active connection
        2. Generates tool calling URL with connected account ID
        3. Updates the stored MCP config
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
            
        Returns:
            Tuple of (success, new_url_config)
        """
        try:
            # Check if user has an active connection
            is_connected, connected_account_id = await composio_connected_accounts_service.check_connection_status(
                user_id, app_key
            )
            
            if not is_connected or not connected_account_id:
                logger.warning(f"User {user_id} does not have active connection for {app_key}")
                return False, None
            
            # Generate tool calling URL
            tool_calling_config = self.generate_tool_calling_url(
                app_key, user_id, connected_account_id
            )
            
            if not tool_calling_config:
                logger.error(f"Failed to generate tool calling URL for {app_key}")
                return False, None
            
            # Store the new configuration
            success = await self.store_mcp_config(user_id, tool_calling_config)
            
            if success:
                logger.info(f"Successfully transitioned {user_id}:{app_key} to tool calling phase")
                return True, tool_calling_config
            else:
                logger.error(f"Failed to store tool calling config for {user_id}:{app_key}")
                return False, None
                
        except Exception as e:
            logger.error(f"Error transitioning to tool calling: {str(e)}")
            return False, None
    
    async def get_current_phase(self, user_id: str, app_key: str) -> Optional[MCPPhase]:
        """
        Get the current phase for a user's app connection.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
            
        Returns:
            Current MCPPhase or None if not found
        """
        try:
            # Get user's agent and MCP configs
            agent_result = self.supabase.table("agents").select("custom_mcps").eq("user_id", user_id).limit(1).execute()
            
            if not agent_result.data:
                return None
            
            custom_mcps = agent_result.data[0].get("custom_mcps", [])
            
            # Find config for this app
            for config in custom_mcps:
                if config.get("metadata", {}).get("app_key") == app_key:
                    phase_str = config.get("metadata", {}).get("phase")
                    if phase_str:
                        return MCPPhase(phase_str)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting current phase: {str(e)}")
            return None


# Global instance
composio_url_manager = ComposioURLManager()
