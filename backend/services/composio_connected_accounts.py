"""
Composio Connected Accounts Service

This service manages the connection between Atlas users and their Composio connected accounts.
It handles entity ID mapping, connection status monitoring, and provides methods to retrieve
connected account information for MCP URL generation.
"""

import asyncio
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from utils.logger import logger
from services.composio_api_client import (
    composio_api_client,
    ComposioConnectedAccount,
)


@dataclass
class UserConnectionInfo:
    """Information about a user's connection to a specific app"""
    
    user_id: str
    app_key: str
    connected_account_id: Optional[str]
    status: str  # "connected", "pending", "disconnected", "error"
    connection_details: Optional[ComposioConnectedAccount] = None
    last_checked: Optional[str] = None


class ComposioConnectedAccountsService:
    """Service for managing Composio connected accounts"""
    
    def __init__(self):
        self._entity_id_mapping: Dict[str, str] = {}
        self._connection_cache: Dict[str, UserConnectionInfo] = {}
    
    def _map_user_to_entity_id(self, user_id: str) -> str:
        """
        Map Atlas user ID to Composio entity ID.
        
        For now, we use the user_id directly as entity_id.
        In the future, this could be enhanced to use a proper mapping table.
        
        Args:
            user_id: Atlas user ID
            
        Returns:
            Composio entity ID
        """
        if user_id in self._entity_id_mapping:
            return self._entity_id_mapping[user_id]
        
        # For now, use user_id directly as entity_id
        entity_id = user_id
        self._entity_id_mapping[user_id] = entity_id
        
        logger.info(f"Mapped user {user_id} to entity {entity_id}")
        return entity_id
    
    def _get_cache_key(self, user_id: str, app_key: str) -> str:
        """Generate cache key for user-app combination"""
        return f"{user_id}:{app_key}"
    
    async def get_user_connection_info(
        self, user_id: str, app_key: str, force_refresh: bool = False
    ) -> UserConnectionInfo:
        """
        Get connection information for a user and specific app.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
            force_refresh: Whether to bypass cache and fetch fresh data
            
        Returns:
            UserConnectionInfo with current connection status
        """
        cache_key = self._get_cache_key(user_id, app_key)
        
        # Check cache first (unless force refresh)
        if not force_refresh and cache_key in self._connection_cache:
            cached_info = self._connection_cache[cache_key]
            logger.info(f"Returning cached connection info for {user_id}:{app_key}")
            return cached_info
        
        # Fetch fresh data from Composio API
        try:
            entity_id = self._map_user_to_entity_id(user_id)
            connected_accounts = await composio_api_client.get_user_connected_accounts_for_app(
                entity_id, app_key
            )
            
            if connected_accounts:
                # User has at least one active connection for this app
                # Use the first active connection
                active_account = connected_accounts[0]
                connection_info = UserConnectionInfo(
                    user_id=user_id,
                    app_key=app_key,
                    connected_account_id=active_account.id,
                    status="connected",
                    connection_details=active_account,
                    last_checked="now()"
                )
            else:
                # No active connections found
                connection_info = UserConnectionInfo(
                    user_id=user_id,
                    app_key=app_key,
                    connected_account_id=None,
                    status="disconnected",
                    connection_details=None,
                    last_checked="now()"
                )
            
            # Cache the result
            self._connection_cache[cache_key] = connection_info
            logger.info(f"Cached connection info for {user_id}:{app_key} - Status: {connection_info.status}")
            
            return connection_info
            
        except Exception as e:
            logger.error(f"Error getting connection info for {user_id}:{app_key}: {str(e)}")
            
            # Return error status
            error_info = UserConnectionInfo(
                user_id=user_id,
                app_key=app_key,
                connected_account_id=None,
                status="error",
                connection_details=None,
                last_checked="now()"
            )
            
            self._connection_cache[cache_key] = error_info
            return error_info
    
    async def check_connection_status(
        self, user_id: str, app_key: str
    ) -> Tuple[bool, Optional[str]]:
        """
        Check if user has an active connection for the specified app.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
            
        Returns:
            Tuple of (is_connected, connected_account_id)
        """
        connection_info = await self.get_user_connection_info(user_id, app_key)
        
        is_connected = connection_info.status == "connected"
        connected_account_id = connection_info.connected_account_id
        
        logger.info(f"Connection status for {user_id}:{app_key} - Connected: {is_connected}, Account ID: {connected_account_id}")
        
        return is_connected, connected_account_id
    
    async def get_all_user_connections(self, user_id: str) -> List[UserConnectionInfo]:
        """
        Get all connections for a user across all apps.
        
        Args:
            user_id: Atlas user ID
            
        Returns:
            List of UserConnectionInfo for all apps
        """
        try:
            entity_id = self._map_user_to_entity_id(user_id)
            all_accounts = await composio_api_client.list_connected_accounts(
                user_ids=[entity_id],
                statuses=["ACTIVE"]
            )
            
            connections = []
            for account in all_accounts:
                connection_info = UserConnectionInfo(
                    user_id=user_id,
                    app_key=account.toolkit_slug,
                    connected_account_id=account.id,
                    status="connected",
                    connection_details=account,
                    last_checked="now()"
                )
                connections.append(connection_info)
                
                # Cache individual connections
                cache_key = self._get_cache_key(user_id, account.toolkit_slug)
                self._connection_cache[cache_key] = connection_info
            
            logger.info(f"Found {len(connections)} active connections for user {user_id}")
            return connections
            
        except Exception as e:
            logger.error(f"Error getting all connections for user {user_id}: {str(e)}")
            return []
    
    async def wait_for_connection(
        self, user_id: str, app_key: str, timeout_seconds: int = 300, poll_interval: int = 5
    ) -> Tuple[bool, Optional[str]]:
        """
        Wait for a user to complete OAuth and establish a connection.
        
        This is useful after initiating the OAuth flow to wait for completion.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
            timeout_seconds: Maximum time to wait (default: 5 minutes)
            poll_interval: How often to check (default: 5 seconds)
            
        Returns:
            Tuple of (success, connected_account_id)
        """
        logger.info(f"Waiting for connection: {user_id}:{app_key} (timeout: {timeout_seconds}s)")
        
        start_time = asyncio.get_event_loop().time()
        
        while True:
            # Check current connection status
            is_connected, connected_account_id = await self.check_connection_status(
                user_id, app_key
            )
            
            if is_connected and connected_account_id:
                logger.info(f"Connection established: {user_id}:{app_key} -> {connected_account_id}")
                return True, connected_account_id
            
            # Check timeout
            elapsed = asyncio.get_event_loop().time() - start_time
            if elapsed >= timeout_seconds:
                logger.warning(f"Timeout waiting for connection: {user_id}:{app_key}")
                return False, None
            
            # Wait before next poll
            await asyncio.sleep(poll_interval)
    
    def clear_cache(self, user_id: Optional[str] = None, app_key: Optional[str] = None):
        """
        Clear connection cache.
        
        Args:
            user_id: If provided, clear cache for specific user
            app_key: If provided (with user_id), clear cache for specific user-app combination
        """
        if user_id and app_key:
            # Clear specific user-app combination
            cache_key = self._get_cache_key(user_id, app_key)
            if cache_key in self._connection_cache:
                del self._connection_cache[cache_key]
                logger.info(f"Cleared cache for {user_id}:{app_key}")
        elif user_id:
            # Clear all cache entries for user
            keys_to_remove = [key for key in self._connection_cache.keys() if key.startswith(f"{user_id}:")]
            for key in keys_to_remove:
                del self._connection_cache[key]
            logger.info(f"Cleared cache for user {user_id}")
        else:
            # Clear all cache
            self._connection_cache.clear()
            logger.info("Cleared all connection cache")


# Global instance
composio_connected_accounts_service = ComposioConnectedAccountsService()
