#!/usr/bin/env python3
"""
Simple runner script for the Composio Two-Phase Flow Test

This script provides an easy way to run the comprehensive test
and see the results of the two-phase MCP implementation.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_composio_two_phase_flow import main

if __name__ == "__main__":
    print("🧪 Composio Two-Phase Flow Test Runner")
    print("=" * 50)
    print()
    
    try:
        results = asyncio.run(main())
        
        # Exit with appropriate code
        if results.get("overall_status") == "SUCCESS":
            print("\n🎉 All tests passed!")
            sys.exit(0)
        else:
            print("\n💥 Some tests failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Test runner failed: {e}")
        sys.exit(1)
