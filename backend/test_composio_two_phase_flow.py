"""
Comprehensive Test for Composio Two-Phase MCP Flow

This test validates the complete implementation of the two-phase Composio MCP integration:

Phase 1: Connection Creation
- Generate helper URL for OAuth
- Store in Supabase
- Monitor for OAuth completion

Phase 2: Tool Calling
- Transition to tool calling URL
- Update Supabase configuration
- Enable actual tool execution

This test works with real Composio servers and validates the entire flow.
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional
from utils.logger import logger
from utils.config import config

# Import all the services we've built
from services.composio_api_client import composio_api_client
from services.composio_connected_accounts import composio_connected_accounts_service
from services.composio_url_manager import composio_url_manager, MCPPhase
from services.composio_connection_monitor import composio_connection_monitor
from services.composio_mcp_manager import composio_mcp_manager


class ComposioTwoPhaseFlowTest:
    """Comprehensive test for the two-phase Composio MCP flow"""

    def __init__(self):
        self.test_user_id = "test-user-two-phase-flow"
        self.test_app_key = "gmail"  # Using Gmail as it has a real server ID
        self.results: Dict[str, Any] = {}

    async def run_complete_test(self) -> Dict[str, Any]:
        """
        Run the complete two-phase flow test.

        Returns:
            Dictionary with test results
        """
        logger.info("🚀 Starting Comprehensive Composio Two-Phase Flow Test")

        try:
            # Test 1: Validate Configuration
            await self._test_configuration()

            # Test 2: Phase 1 - Connection Creation
            await self._test_phase_1_connection_creation()

            # Test 3: Connected Accounts Service
            await self._test_connected_accounts_service()

            # Test 4: Connection Status Monitoring
            await self._test_connection_monitoring()

            # Test 5: Phase 2 - Tool Calling (Simulation)
            await self._test_phase_2_tool_calling()

            # Test 6: URL Manager
            await self._test_url_manager()

            # Test 7: MCP Manager Integration
            await self._test_mcp_manager_integration()

            # Test 8: API Endpoints (if available)
            await self._test_api_endpoints()

            # Test 9: Cleanup
            await self._test_cleanup()

            self.results["overall_status"] = "SUCCESS"
            self.results["message"] = "All tests passed successfully"

        except Exception as e:
            logger.error(f"Test failed with error: {str(e)}")
            self.results["overall_status"] = "FAILED"
            self.results["error"] = str(e)
            self.results["message"] = "Test suite failed"

        return self.results

    async def _test_configuration(self):
        """Test 1: Validate that all configurations are properly loaded"""
        logger.info("📋 Test 1: Validating Configuration")

        try:
            # Check if server registry is loaded
            server_id = composio_url_manager._get_server_id(self.test_app_key)
            assert server_id is not None, f"No server ID found for {self.test_app_key}"
            assert (
                server_id == "3adcafb4-4a7d-4b97-bb26-3be782883e06"
            ), f"Unexpected server ID: {server_id}"

            # Check if API client is configured
            assert (
                config.COMPOSIO_API_KEY is not None
            ), "Composio API key not configured"

            self.results["test_1_configuration"] = {
                "status": "PASSED",
                "server_id": server_id,
                "api_key_configured": bool(config.COMPOSIO_API_KEY),
            }

            logger.info("✅ Test 1 PASSED: Configuration validated")

        except Exception as e:
            self.results["test_1_configuration"] = {"status": "FAILED", "error": str(e)}
            raise

    async def _test_phase_1_connection_creation(self):
        """Test 2: Phase 1 - Connection Creation URL Generation"""
        logger.info("🔗 Test 2: Phase 1 - Connection Creation")

        try:
            # Generate connection creation URL
            connection_config = composio_url_manager.generate_connection_creation_url(
                self.test_app_key, self.test_user_id
            )

            assert (
                connection_config is not None
            ), "Failed to generate connection creation URL"
            assert (
                connection_config.phase == MCPPhase.CONNECTION_CREATION
            ), "Wrong phase"
            assert "user_id=" in connection_config.url, "URL missing user_id parameter"
            assert (
                "include_composio_helper_actions=true" in connection_config.url
            ), "URL missing helper actions"

            # Store the configuration (simulate Supabase storage)
            # Note: This might fail if Supabase is not configured, which is OK for testing
            try:
                success = await composio_url_manager.store_mcp_config(
                    self.test_user_id, connection_config
                )
                storage_result = "SUCCESS" if success else "FAILED"
            except Exception as e:
                storage_result = f"ERROR: {str(e)}"

            self.results["test_2_phase_1"] = {
                "status": "PASSED",
                "url": connection_config.url,
                "phase": connection_config.phase.value,
                "server_id": connection_config.server_id,
                "storage_result": storage_result,
            }

            logger.info("✅ Test 2 PASSED: Phase 1 connection creation URL generated")

        except Exception as e:
            self.results["test_2_phase_1"] = {"status": "FAILED", "error": str(e)}
            raise

    async def _test_connected_accounts_service(self):
        """Test 3: Connected Accounts Service"""
        logger.info("👤 Test 3: Connected Accounts Service")

        try:
            # Test connection info retrieval (will show no connection initially)
            connection_info = (
                await composio_connected_accounts_service.get_user_connection_info(
                    self.test_user_id, self.test_app_key
                )
            )

            assert connection_info is not None, "Failed to get connection info"
            # Should be disconnected initially
            assert connection_info.status in [
                "disconnected",
                "error",
            ], f"Unexpected status: {connection_info.status}"

            # Test connection status check
            is_connected, connected_account_id = (
                await composio_connected_accounts_service.check_connection_status(
                    self.test_user_id, self.test_app_key
                )
            )

            assert not is_connected, "Should not be connected initially"
            assert (
                connected_account_id is None
            ), "Should not have connected account ID initially"

            self.results["test_3_connected_accounts"] = {
                "status": "PASSED",
                "initial_connection_status": connection_info.status,
                "is_connected": is_connected,
                "connected_account_id": connected_account_id,
            }

            logger.info("✅ Test 3 PASSED: Connected Accounts Service working")

        except Exception as e:
            self.results["test_3_connected_accounts"] = {
                "status": "FAILED",
                "error": str(e),
            }
            raise

    async def _test_connection_monitoring(self):
        """Test 4: Connection Status Monitoring"""
        logger.info("👁️ Test 4: Connection Status Monitoring")

        try:
            # Add a pending connection to monitor
            composio_connection_monitor.add_pending_connection(
                self.test_user_id, self.test_app_key
            )

            # Check that it was added
            pending_connections = composio_connection_monitor.get_pending_connections()
            key = f"{self.test_user_id}:{self.test_app_key}"

            assert key in pending_connections, "Pending connection not added"

            # Test monitoring start/stop
            composio_connection_monitor.start_monitoring()
            assert composio_connection_monitor._is_monitoring, "Monitoring not started"

            # Wait a short time to let monitoring run
            await asyncio.sleep(2)

            # Remove the pending connection
            composio_connection_monitor.remove_pending_connection(
                self.test_user_id, self.test_app_key
            )

            # Stop monitoring
            composio_connection_monitor.stop_monitoring()
            assert (
                not composio_connection_monitor._is_monitoring
            ), "Monitoring not stopped"

            self.results["test_4_monitoring"] = {
                "status": "PASSED",
                "pending_connection_added": True,
                "monitoring_started": True,
                "monitoring_stopped": True,
            }

            logger.info("✅ Test 4 PASSED: Connection monitoring working")

        except Exception as e:
            self.results["test_4_monitoring"] = {"status": "FAILED", "error": str(e)}
            raise

    async def _test_phase_2_tool_calling(self):
        """Test 5: Phase 2 - Tool Calling URL Generation (Simulation)"""
        logger.info("🔧 Test 5: Phase 2 - Tool Calling (Simulation)")

        try:
            # Simulate having a connected account ID
            mock_connected_account_id = "ca_mock_account_12345"

            # Generate tool calling URL
            tool_calling_config = composio_url_manager.generate_tool_calling_url(
                self.test_app_key, self.test_user_id, mock_connected_account_id
            )

            assert (
                tool_calling_config is not None
            ), "Failed to generate tool calling URL"
            assert tool_calling_config.phase == MCPPhase.TOOL_CALLING, "Wrong phase"
            assert "/mcp?" in tool_calling_config.url, "URL missing /mcp endpoint"
            assert (
                f"connected_account_id={mock_connected_account_id}"
                in tool_calling_config.url
            ), "URL missing connected_account_id"

            self.results["test_5_phase_2"] = {
                "status": "PASSED",
                "url": tool_calling_config.url,
                "phase": tool_calling_config.phase.value,
                "connected_account_id": tool_calling_config.connected_account_id,
            }

            logger.info("✅ Test 5 PASSED: Phase 2 tool calling URL generated")

        except Exception as e:
            self.results["test_5_phase_2"] = {"status": "FAILED", "error": str(e)}
            raise

    async def _test_url_manager(self):
        """Test 6: URL Manager Functionality"""
        logger.info("🔗 Test 6: URL Manager")

        try:
            # Test phase detection
            current_phase = await composio_url_manager.get_current_phase(
                self.test_user_id, self.test_app_key
            )
            # May be None if no configuration exists, which is OK

            # Test transition simulation (without actual OAuth)
            # This will fail because no real OAuth, but we can test the logic
            try:
                success, config = await composio_url_manager.transition_to_tool_calling(
                    self.test_user_id, self.test_app_key
                )
                transition_result = "SUCCESS" if success else "FAILED_AS_EXPECTED"
            except Exception as e:
                transition_result = f"FAILED_AS_EXPECTED: {str(e)}"

            self.results["test_6_url_manager"] = {
                "status": "PASSED",
                "current_phase": current_phase.value if current_phase else None,
                "transition_test": transition_result,
            }

            logger.info("✅ Test 6 PASSED: URL Manager functionality tested")

        except Exception as e:
            self.results["test_6_url_manager"] = {"status": "FAILED", "error": str(e)}
            raise

    async def _test_mcp_manager_integration(self):
        """Test 7: MCP Manager Integration"""
        logger.info("🎯 Test 7: MCP Manager Integration")

        try:
            # Test connection initiation
            success, connection_info = await composio_mcp_manager.initiate_connection(
                self.test_user_id, self.test_app_key
            )

            # This might fail due to Supabase configuration, which is OK for testing
            initiation_result = "SUCCESS" if success else "FAILED_CONFIG_ISSUE"

            # Test getting current connection
            found, current_connection = (
                await composio_mcp_manager.get_current_connection(
                    self.test_user_id, self.test_app_key
                )
            )

            # Test supported apps
            supported_apps = await composio_mcp_manager.list_supported_apps()
            assert isinstance(supported_apps, list), "Supported apps should be a list"
            assert (
                self.test_app_key in supported_apps
            ), f"{self.test_app_key} should be in supported apps"

            self.results["test_7_mcp_manager"] = {
                "status": "PASSED",
                "initiation_result": initiation_result,
                "connection_found": found,
                "supported_apps": supported_apps,
                "connection_status": (
                    current_connection.status if current_connection else None
                ),
            }

            logger.info("✅ Test 7 PASSED: MCP Manager integration tested")

        except Exception as e:
            self.results["test_7_mcp_manager"] = {"status": "FAILED", "error": str(e)}
            raise

    async def _test_api_endpoints(self):
        """Test 8: API Endpoints (if available)"""
        logger.info("🌐 Test 8: API Endpoints")

        try:
            # Note: This would require FastAPI to be running
            # For now, we'll just test that the imports work
            from api.composio_mcp import (
                CreateMCPConnectionRequest,
                CreateMCPConnectionResponse,
                CheckConnectionStatusRequest,
                CheckConnectionStatusResponse,
            )

            # Test request/response model creation
            request = CreateMCPConnectionRequest(app_key=self.test_app_key)
            assert request.app_key == self.test_app_key, "Request model creation failed"

            status_request = CheckConnectionStatusRequest(app_key=self.test_app_key)
            assert (
                status_request.app_key == self.test_app_key
            ), "Status request model creation failed"

            self.results["test_8_api_endpoints"] = {
                "status": "PASSED",
                "models_imported": True,
                "request_creation": True,
                "note": "Full API testing requires running FastAPI server",
            }

            logger.info("✅ Test 8 PASSED: API endpoint models tested")

        except Exception as e:
            self.results["test_8_api_endpoints"] = {"status": "FAILED", "error": str(e)}
            raise

    async def _test_cleanup(self):
        """Test 9: Cleanup"""
        logger.info("🧹 Test 9: Cleanup")

        try:
            # Clear any cached data
            composio_connected_accounts_service.clear_cache(
                self.test_user_id, self.test_app_key
            )

            # Stop monitoring if running
            if composio_connection_monitor._is_monitoring:
                composio_connection_monitor.stop_monitoring()

            # Remove any pending connections
            composio_connection_monitor.remove_pending_connection(
                self.test_user_id, self.test_app_key
            )

            self.results["test_9_cleanup"] = {
                "status": "PASSED",
                "cache_cleared": True,
                "monitoring_stopped": True,
                "pending_connections_removed": True,
            }

            logger.info("✅ Test 9 PASSED: Cleanup completed")

        except Exception as e:
            self.results["test_9_cleanup"] = {"status": "FAILED", "error": str(e)}
            raise

    def print_results(self):
        """Print formatted test results"""
        print("\n" + "=" * 80)
        print("🧪 COMPOSIO TWO-PHASE FLOW TEST RESULTS")
        print("=" * 80)

        for test_name, result in self.results.items():
            if test_name in ["overall_status", "message", "error"]:
                continue

            status = result.get("status", "UNKNOWN")
            emoji = "✅" if status == "PASSED" else "❌"
            print(f"{emoji} {test_name.replace('_', ' ').title()}: {status}")

            if status == "FAILED" and "error" in result:
                print(f"   Error: {result['error']}")

        print("\n" + "-" * 80)
        overall_status = self.results.get("overall_status", "UNKNOWN")
        overall_emoji = "🎉" if overall_status == "SUCCESS" else "💥"
        print(f"{overall_emoji} OVERALL STATUS: {overall_status}")
        print(f"📝 MESSAGE: {self.results.get('message', 'No message')}")

        if "error" in self.results:
            print(f"❌ ERROR: {self.results['error']}")

        print("=" * 80)


async def main():
    """Run the comprehensive test"""
    test = ComposioTwoPhaseFlowTest()

    print("🚀 Starting Composio Two-Phase Flow Integration Test...")
    print("This test validates the complete implementation of the two-phase MCP flow.")
    print()

    results = await test.run_complete_test()

    # Print results
    test.print_results()

    # Save results to file
    with open("composio_two_phase_test_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\n📄 Detailed results saved to: composio_two_phase_test_results.json")

    return results


if __name__ == "__main__":
    asyncio.run(main())
