"""
Composio MCP Integration API Endpoints

This module provides FastAPI endpoints for integrating Composio MCP servers
with our existing MCP architecture and authentication flow using constants-based URLs.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from utils.auth_utils import get_current_user_id_from_jwt
from utils.logger import logger
from services.composio_integration import (
    composio_mcp_service,
    ComposioMCPConnection,
    composio_tool_executor,
)
from services.mcp_custom import discover_custom_tools
from constants.composio_mcp_constants import get_supported_composio_apps
from services.composio_mcp_manager import (
    composio_mcp_manager,
    UserMCPConnection,
)
from services.composio_connected_accounts import composio_connected_accounts_service
from services.composio_connection_monitor import composio_connection_monitor

router = APIRouter(prefix="/composio-mcp", tags=["composio-mcp"])


class CreateMCPConnectionRequest(BaseModel):
    """Request model for creating a Composio MCP connection"""

    app_key: str  # e.g., "gmail", "slack", "github"


class CreateMCPConnectionResponse(BaseModel):
    """Response model for MCP connection creation"""

    success: bool
    app_key: str
    qualified_name: Optional[str] = None
    mcp_url: Optional[str] = None
    auth_url: Optional[str] = None
    session_uuid: Optional[str] = None
    error: Optional[str] = None
    message: str
    phase: Optional[str] = None  # "connection_creation" or "tool_calling"
    status: Optional[str] = None  # "pending_connection", "connected", "error"
    connected_account_id: Optional[str] = None
    server_id: Optional[str] = None


class ListUserConnectionsResponse(BaseModel):
    """Response model for listing user's Composio MCP connections"""

    success: bool
    connections: List[Dict[str, Any]]
    total: int


class DiscoverComposioToolsRequest(BaseModel):
    """Request model for discovering tools from a Composio MCP connection"""

    app_key: str  # e.g., "gmail", "slack", "github"


class DiscoverComposioToolsResponse(BaseModel):
    """Response model for Composio MCP tool discovery"""

    success: bool
    app_key: str
    tools: List[Dict[str, Any]]
    count: int
    mcp_url: Optional[str] = None
    error: Optional[str] = None


class UpdateComposioToolsRequest(BaseModel):
    """Request model for updating selected tools for a Composio MCP connection"""

    app_key: str
    selected_tools: List[str]  # List of tool names to enable


class UpdateComposioToolsResponse(BaseModel):
    """Response model for updating Composio MCP tools"""

    success: bool
    app_key: str
    enabled_tools: List[str]
    message: str
    error: Optional[str] = None


class GetSupportedAppsResponse(BaseModel):
    """Response model for getting supported Composio apps"""

    success: bool
    apps: List[Dict[str, Any]]
    total: int
    message: str


class InitiateAuthRequest(BaseModel):
    """Request model for initiating authentication for a Composio MCP connection"""

    app_key: str  # e.g., "gmail", "slack", "github"


class InitiateAuthResponse(BaseModel):
    """Response model for Composio MCP authentication initiation"""

    success: bool
    app_key: str
    tool_name: str
    redirect_url: Optional[str] = None
    connection_id: Optional[str] = None
    instruction: Optional[str] = None
    message: str
    error: Optional[str] = None


class CheckConnectionStatusRequest(BaseModel):
    """Request model for checking connection status"""

    app_key: str


class CheckConnectionStatusResponse(BaseModel):
    """Response model for connection status check"""

    success: bool
    app_key: str
    is_connected: bool
    connected_account_id: Optional[str] = None
    phase: Optional[str] = None
    status: Optional[str] = None
    mcp_url: Optional[str] = None
    message: str


class UpdateConnectionRequest(BaseModel):
    """Request model for updating connection after OAuth"""

    app_key: str


class UpdateConnectionResponse(BaseModel):
    """Response model for connection update"""

    success: bool
    app_key: str
    updated: bool
    phase: Optional[str] = None
    status: Optional[str] = None
    mcp_url: Optional[str] = None
    connected_account_id: Optional[str] = None
    message: str


@router.post("/create-connection", response_model=CreateMCPConnectionResponse)
async def create_composio_mcp_connection(
    request: CreateMCPConnectionRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Create an MCP connection using the new two-phase flow.

    This endpoint:
    1. Checks if user already has an active connection (Phase 2)
    2. If not, initiates connection creation (Phase 1)
    3. Stores the connection in Supabase
    4. Starts monitoring for OAuth completion
    5. Returns the appropriate URL for the current phase

    Args:
        request: Contains the app_key (e.g., "gmail", "slack")
        user_id: Current user ID from JWT token

    Returns:
        Response with MCP URL and phase information
    """
    try:
        logger.info(
            f"Creating Composio MCP connection for user {user_id}, app {request.app_key}"
        )

        # Check if user already has a connection for this app
        found, current_connection = await composio_mcp_manager.get_current_connection(
            user_id, request.app_key
        )

        if found and current_connection.status == "connected":
            # User already has an active connection in Phase 2
            logger.info(
                f"User {user_id} already has active connection for {request.app_key}"
            )
            return CreateMCPConnectionResponse(
                success=True,
                app_key=request.app_key,
                qualified_name=f"{request.app_key.title()} MCP",
                mcp_url=current_connection.mcp_url,
                message="Connection already active",
                phase=current_connection.phase.value,
                status=current_connection.status,
                connected_account_id=current_connection.connected_account_id,
                server_id=current_connection.server_id,
            )

        # Initiate new connection (Phase 1)
        success, connection_info = await composio_mcp_manager.initiate_connection(
            user_id, request.app_key
        )

        if success:
            logger.info(f"Successfully created MCP connection for {request.app_key}")
            return CreateMCPConnectionResponse(
                success=True,
                app_key=connection_info.app_key,
                qualified_name=f"{request.app_key.title()} MCP",
                mcp_url=connection_info.mcp_url,
                auth_url=connection_info.mcp_url,  # The MCP URL is the auth URL in Phase 1
                message=f"Connection initiated for {request.app_key}. Please complete OAuth authentication.",
                phase=connection_info.phase.value,
                status=connection_info.status,
                server_id=connection_info.server_id,
            )
        else:
            error_msg = (
                connection_info.metadata.get("error", "Unknown error occurred")
                if connection_info.metadata
                else "Unknown error"
            )
            logger.error(f"Failed to initiate MCP connection: {error_msg}")
            return CreateMCPConnectionResponse(
                success=False,
                app_key=request.app_key,
                error=error_msg,
                message=f"Failed to initiate MCP connection for {request.app_key}",
                phase=(
                    connection_info.phase.value
                    if connection_info
                    else "connection_creation"
                ),
                status="error",
            )

    except Exception as e:
        logger.error(f"Error in create_composio_mcp_connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/check-connection-status", response_model=CheckConnectionStatusResponse)
async def check_connection_status(
    request: CheckConnectionStatusRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Check the current connection status for a user and app.

    This endpoint checks:
    1. Current phase (connection_creation or tool_calling)
    2. Connection status (pending_connection, connected, error)
    3. Whether OAuth has been completed
    4. Current MCP URL
    """
    try:
        logger.info(
            f"Checking connection status for user {user_id}, app {request.app_key}"
        )

        # Get current connection info
        found, connection_info = await composio_mcp_manager.get_current_connection(
            user_id, request.app_key
        )

        if not found:
            return CheckConnectionStatusResponse(
                success=True,
                app_key=request.app_key,
                is_connected=False,
                message="No connection found for this app",
            )

        # Check if user has completed OAuth (for Phase 1 connections)
        if connection_info.phase.value == "connection_creation":
            is_connected, connected_account_id = (
                await composio_connected_accounts_service.check_connection_status(
                    user_id, request.app_key
                )
            )

            if is_connected:
                # OAuth completed but not yet transitioned - trigger update
                success, updated_connection = (
                    await composio_mcp_manager.check_and_update_connection(
                        user_id, request.app_key
                    )
                )

                if success:
                    connection_info = updated_connection

        return CheckConnectionStatusResponse(
            success=True,
            app_key=request.app_key,
            is_connected=(connection_info.status == "connected"),
            connected_account_id=connection_info.connected_account_id,
            phase=connection_info.phase.value,
            status=connection_info.status,
            mcp_url=connection_info.mcp_url,
            message=f"Connection status: {connection_info.status}",
        )

    except Exception as e:
        logger.error(f"Error checking connection status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/update-connection", response_model=UpdateConnectionResponse)
async def update_connection(
    request: UpdateConnectionRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Update connection from Phase 1 to Phase 2 after OAuth completion.

    This endpoint:
    1. Checks if OAuth has been completed
    2. Transitions from connection_creation to tool_calling phase
    3. Updates the MCP URL with connected_account_id
    4. Updates the stored configuration in Supabase
    """
    try:
        logger.info(f"Updating connection for user {user_id}, app {request.app_key}")

        # Check and update connection
        success, updated_connection = (
            await composio_mcp_manager.check_and_update_connection(
                user_id, request.app_key
            )
        )

        if success:
            return UpdateConnectionResponse(
                success=True,
                app_key=request.app_key,
                updated=True,
                phase=updated_connection.phase.value,
                status=updated_connection.status,
                mcp_url=updated_connection.mcp_url,
                connected_account_id=updated_connection.connected_account_id,
                message="Connection successfully updated to tool calling phase",
            )
        else:
            return UpdateConnectionResponse(
                success=False,
                app_key=request.app_key,
                updated=False,
                phase=(
                    updated_connection.phase.value
                    if updated_connection
                    else "connection_creation"
                ),
                status=updated_connection.status if updated_connection else "error",
                message="Failed to update connection or OAuth not yet completed",
            )

    except Exception as e:
        logger.error(f"Error updating connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user-connections", response_model=ListUserConnectionsResponse)
async def list_user_composio_connections(
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    List all Composio MCP connections for the current user.

    Returns connections stored in the mcp_oauth_tokens table with
    qualified_name starting with "composio/".
    """
    try:
        logger.info(f"Listing Composio MCP connections for user {user_id}")

        # Use the service method to get connections
        connections = await composio_mcp_service.list_user_mcp_connections(user_id)

        return ListUserConnectionsResponse(
            success=True, connections=connections, total=len(connections)
        )

    except Exception as e:
        logger.error(f"Error listing user connections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/connection/{app_key}")
async def delete_composio_connection(
    app_key: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """
    Delete a Composio MCP connection for the user.

    Args:
        app_key: The app key to delete (e.g., "gmail")
        user_id: Current user ID from JWT token
    """
    try:
        logger.info(
            f"Deleting Composio MCP connection for user {user_id}, app {app_key}"
        )

        # Use the service method to delete connection
        deleted = await composio_mcp_service.delete_user_mcp_connection(
            user_id, app_key
        )

        if deleted:
            return {
                "success": True,
                "message": f"Successfully deleted connection for {app_key}",
            }
        else:
            return {"success": False, "message": f"No connection found for {app_key}"}

    except Exception as e:
        logger.error(f"Error deleting connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/supported-apps", response_model=GetSupportedAppsResponse)
async def get_supported_apps():
    """
    Get list of supported Composio apps from our constants file.

    This endpoint returns all apps defined in our constants file with
    standardized metadata for each app.
    """
    try:
        # Get supported apps from our constants file
        supported_app_keys = get_supported_composio_apps()
        logger.info(f"Found {len(supported_app_keys)} supported apps in constants file")

        # Create app metadata from our constants
        supported_apps = []

        # Define app metadata for our supported apps
        app_metadata = {
            "gmail": {
                "name": "Gmail",
                "description": "Connect to Gmail for email management and automation",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/gmail.svg",
                "category": "communication",
                "tool_count": 23,
                "popular": True,
            },
            "github": {
                "name": "GitHub",
                "description": "Connect to GitHub for code management and automation",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/github.png",
                "category": "development",
                "tool_count": 910,
                "popular": True,
            },
            "slack": {
                "name": "Slack",
                "description": "Connect to Slack for team communication and automation",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/slack.svg",
                "category": "communication",
                "tool_count": 174,
                "popular": True,
            },
            "notion": {
                "name": "Notion",
                "description": "Connect to Notion for productivity and note management",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg",
                "category": "productivity",
                "tool_count": 45,
                "popular": True,
            },
            "linear": {
                "name": "Linear",
                "description": "Connect to Linear for issue tracking and project management",
                "icon": "https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/linear.svg",
                "category": "productivity",
                "tool_count": 32,
                "popular": True,
            },
        }

        for app_key in supported_app_keys:
            # Get metadata if available, otherwise use defaults
            metadata = app_metadata.get(
                app_key,
                {
                    "name": app_key.replace("_", " ").title(),
                    "description": f"Connect to {app_key.replace('_', ' ').title()}",
                    "icon": "🔗",  # Default emoji icon
                    "category": "other",
                    "tool_count": 0,
                    "popular": False,
                },
            )

            supported_apps.append(
                {
                    "key": app_key,
                    "name": metadata["name"],
                    "description": metadata["description"],
                    "icon": metadata["icon"],
                    "category": metadata["category"],
                    "tool_count": metadata["tool_count"],
                    "usage_count": 0,  # We don't track usage count
                    "popular": metadata["popular"],
                }
            )

        logger.info(f"Generated {len(supported_apps)} apps from constants file")

        return GetSupportedAppsResponse(
            success=True,
            apps=supported_apps,
            total=len(supported_apps),
            message=f"Successfully loaded {len(supported_apps)} supported apps from constants",
        )

    except Exception as e:
        logger.error(f"Error loading apps from constants file: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to load supported apps: {str(e)}"
        )


@router.post("/discover-tools", response_model=DiscoverComposioToolsResponse)
async def discover_composio_tools(
    request: DiscoverComposioToolsRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
) -> DiscoverComposioToolsResponse:
    """
    Discover available tools from an existing Composio MCP connection.

    This endpoint reuses the existing per-agent MCP discovery architecture.
    It looks up the Composio MCP URL from the user's default agent and
    discovers available tools using the same flow as custom HTTP MCPs.
    """
    try:
        logger.info(
            f"Discovering tools for Composio app: {request.app_key}, user: {user_id}"
        )

        # Get the Composio MCP connection (no storage yet)
        # Use the new API-based approach for testing
        connection = await composio_mcp_service.create_user_mcp_connection_no_storage(
            user_id, request.app_key, use_api=True
        )

        if not connection.success or not connection.mcp_url:
            return DiscoverComposioToolsResponse(
                success=False,
                app_key=request.app_key,
                tools=[],
                count=0,
                error="Composio MCP connection not found. Please create the connection first.",
            )

        # Use the existing discover_custom_tools function with HTTP type
        # This is the EXACT same function used for per-agent custom MCP discovery
        discovery_result = await discover_custom_tools(
            request_type="http", config={"url": connection.mcp_url}
        )

        logger.info(
            f"Discovered {len(discovery_result['tools'])} tools for {request.app_key}"
        )

        return DiscoverComposioToolsResponse(
            success=True,
            app_key=request.app_key,
            tools=discovery_result["tools"],
            count=discovery_result["count"],
            mcp_url=connection.mcp_url,
        )

    except Exception as e:
        logger.error(f"Error discovering Composio tools for {request.app_key}: {e}")
        return DiscoverComposioToolsResponse(
            success=False, app_key=request.app_key, tools=[], count=0, error=str(e)
        )


@router.post("/update-tools", response_model=UpdateComposioToolsResponse)
async def update_composio_tools(
    request: UpdateComposioToolsRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
) -> UpdateComposioToolsResponse:
    """
    Update the selected tools for a Composio MCP connection in the default agent.

    This mirrors the exact per-agent MCP tool selection flow, storing the
    selected tools in the default agent's custom_mcps enabledTools array.
    """
    try:
        logger.info(
            f"Updating tools for Composio app: {request.app_key}, user: {user_id}"
        )
        logger.info(f"Selected tools: {request.selected_tools}")

        # Update the default agent's Composio MCP with selected tools
        success = await composio_mcp_service.update_mcp_enabled_tools(
            user_id, request.app_key, request.selected_tools
        )

        if success:
            return UpdateComposioToolsResponse(
                success=True,
                app_key=request.app_key,
                enabled_tools=request.selected_tools,
                message=f"Successfully updated {len(request.selected_tools)} enabled tools for {request.app_key}",
            )
        else:
            return UpdateComposioToolsResponse(
                success=False,
                app_key=request.app_key,
                enabled_tools=[],
                message="Failed to update enabled tools",
                error="Could not update default agent configuration",
            )

    except Exception as e:
        logger.error(f"Error updating Composio tools for {request.app_key}: {e}")
        return UpdateComposioToolsResponse(
            success=False,
            app_key=request.app_key,
            enabled_tools=[],
            message="Failed to update enabled tools",
            error=str(e),
        )


@router.post("/initiate-auth", response_model=InitiateAuthResponse)
async def initiate_composio_auth(
    request: InitiateAuthRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
) -> InitiateAuthResponse:
    """
    Initiate authentication for a Composio MCP connection.

    This endpoint:
    1. Retrieves the MCP URL from the default agent's custom_mcps
    2. Connects to the Composio MCP server using HTTP client
    3. Calls the {APP}-INITIATE-CONNECTION tool dynamically
    4. Extracts and returns the authentication redirect URL

    This completes the 1-click authentication flow:
    create connection → select tools → initiate auth → get redirect URL

    Args:
        request: Contains the app_key (e.g., "gmail", "slack")
        user_id: Current user ID from JWT token

    Returns:
        Response with redirect_url for frontend to open for user authentication
    """
    try:
        logger.info(
            f"Initiating authentication for Composio app: {request.app_key}, user: {user_id}"
        )

        # Execute the initiate connection tool using our new service
        result = await composio_tool_executor.execute_initiate_connection_tool(
            user_id=user_id, app_key=request.app_key
        )

        if result.success:
            logger.info(
                f"Successfully initiated auth for {request.app_key}, redirect URL: {result.redirect_url}"
            )
            return InitiateAuthResponse(
                success=True,
                app_key=result.app_key,
                tool_name=result.tool_name,
                redirect_url=result.redirect_url,
                connection_id=result.connection_id,
                instruction=result.instruction,
                message=f"Authentication initiated for {request.app_key}. Use redirect_url to complete authentication.",
            )
        else:
            logger.error(
                f"Failed to initiate auth for {request.app_key}: {result.error}"
            )
            return InitiateAuthResponse(
                success=False,
                app_key=result.app_key,
                tool_name=result.tool_name,
                message=f"Failed to initiate authentication for {request.app_key}",
                error=result.error,
            )

    except Exception as e:
        logger.error(f"Error in initiate_composio_auth: {e}")
        return InitiateAuthResponse(
            success=False,
            app_key=request.app_key,
            tool_name=f"{request.app_key.upper()}-INITIATE-CONNECTION",
            message=f"Failed to initiate authentication for {request.app_key}",
            error=str(e),
        )


@router.post("/refresh-connection/{app_key}")
async def refresh_mcp_connection(
    app_key: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """
    Refresh MCP connection after OAuth authentication is completed.

    This endpoint should be called after the user completes OAuth authentication
    to ensure the MCP server URL reflects the authenticated state.
    """
    try:
        logger.info(f"Refreshing MCP connection for user {user_id}, app {app_key}")

        success = await composio_mcp_service.refresh_mcp_connection_after_auth(
            user_id, app_key
        )

        if success:
            return {
                "success": True,
                "message": f"Successfully refreshed MCP connection for {app_key}",
                "app_key": app_key,
            }
        else:
            return {
                "success": False,
                "message": f"Failed to refresh MCP connection for {app_key}",
                "app_key": app_key,
                "error": "Could not update MCP URL after authentication",
            }

    except Exception as e:
        logger.error(f"Error refreshing MCP connection for {app_key}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/debug-session/{app_key}")
async def debug_session_uuid(
    app_key: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Debug endpoint to check session UUID consistency"""
    try:
        # Generate session UUID using the same method
        session_uuid = composio_mcp_service._generate_session_uuid(user_id, app_key)

        # Check what's stored in Supabase
        connections = await composio_mcp_service.list_user_mcp_connections(user_id)
        stored_connection = next(
            (conn for conn in connections if conn["app_key"] == app_key), None
        )

        return {
            "user_id": user_id,
            "app_key": app_key,
            "generated_session_uuid": session_uuid,
            "stored_connection": stored_connection,
            "mcp_url_would_be": f"https://mcp.composio.dev/partner/composio/{app_key}/mcp?customerId={session_uuid}",
        }

    except Exception as e:
        logger.error(f"Error in debug session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check for Composio MCP integration"""
    return {
        "status": "healthy",
        "service": "composio_mcp_integration",
        "version": "1.0.0",
    }
